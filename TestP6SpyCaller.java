import java.lang.reflect.Method;

/**
 * Test P6Spy caller detection functionality
 */
public class TestP6SpyCaller {
    
    public static void main(String[] args) {
        System.out.println("=== Testing P6Spy Caller Detection ===");
        
        try {
            // Load the P6SpyLogger class
            Class<?> loggerClass = Class.forName("com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger");
            Object logger = loggerClass.newInstance();
            
            // Get the getCallerInfo method
            Method getCallerInfoMethod = loggerClass.getDeclaredMethod("getCallerInfo");
            getCallerInfoMethod.setAccessible(true);
            
            // Test from a mock service method
            String callerInfo = testFromMockService(getCallerInfoMethod, logger);
            System.out.println("Caller info from mock service: " + callerInfo);
            
        } catch (Exception e) {
            System.out.println("Error testing P6Spy caller detection: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Mock service method to test caller detection
     */
    private static String testFromMockService(Method getCallerInfoMethod, Object logger) throws Exception {
        return testFromMockManager(getCallerInfoMethod, logger);
    }
    
    /**
     * Mock manager method to test caller detection
     */
    private static String testFromMockManager(Method getCallerInfoMethod, Object logger) throws Exception {
        return (String) getCallerInfoMethod.invoke(logger);
    }
}
