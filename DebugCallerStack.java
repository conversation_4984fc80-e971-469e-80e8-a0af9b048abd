import java.util.Arrays;

/**
 * Debug tool for caller stack analysis
 * Analyze why p6spy shows PersistenceExceptionTranslationInterceptor instead of business code
 */
public class DebugCallerStack {

    public static void main(String[] args) {
        System.out.println("=== Caller Stack Debug Tool ===");
        System.out.println("Simulating p6spy caller stack analysis...\n");

        // Analyze current caller stack detection logic
        analyzeCurrentLogic();

        System.out.println("\n=== Improved Caller Stack Detection Logic ===");

        // Test improved caller stack detection logic
        analyzeImprovedLogic();
    }
    
    /**
     * Analyze current caller stack detection logic
     */
    private static void analyzeCurrentLogic() {
        System.out.println("Current logic analysis:");

        // Mock typical call stack
        String[] mockStack = {
            "java.lang.Thread.getStackTrace",
            "com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger.getCallerInfo",
            "com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger.formatMessage",
            "com.p6spy.engine.spy.P6SpyPreparedStatement.execute",
            "com.zaxxer.hikari.pool.ProxyPreparedStatement.execute",
            "org.apache.ibatis.executor.statement.PreparedStatementHandler.query",
            "org.apache.ibatis.executor.statement.BaseStatementHandler.query",
            "org.apache.ibatis.executor.CachingExecutor.query",
            "org.apache.ibatis.session.defaults.DefaultSqlSession.selectList",
            "org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne",
            "org.apache.ibatis.binding.MapperMethod.execute",
            "org.apache.ibatis.binding.MapperProxy.invoke",
            "com.sun.proxy.$Proxy123.selectByUserId",
            "org.springframework.transaction.interceptor.TransactionInterceptor.invoke",
            "org.springframework.aop.framework.ReflectiveMethodInvocation.proceed",
            "org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke",
            "org.springframework.aop.framework.ReflectiveMethodInvocation.proceed",
            "org.springframework.aop.framework.JdkDynamicAopProxy.invoke",
            "com.sun.proxy.$Proxy124.selectByUserId",
            "com.tuowan.yeliao.user.manager.impl.UserMessageSettingManagerImpl.getByUserId",
            "com.tuowan.yeliao.user.service.impl.SignInServiceImpl.saveSignRemind",
            "com.tuowan.yeliao.user.controller.SignInController.remind"
        };

        System.out.println("Mock call stack:");
        for (int i = 0; i < mockStack.length; i++) {
            System.out.printf("%2d. %s\n", i, mockStack[i]);
        }

        System.out.println("\nCurrent logic detection result:");
        String result = getCurrentLogicResult(mockStack);
        System.out.println("Detected caller: " + result);
    }
    
    /**
     * Current logic detection result
     */
    private static String getCurrentLogicResult(String[] stack) {
        for (String className : stack) {
            if (isFrameworkClassCurrent(className) || isProxyClassCurrent(className)) {
                continue;
            }

            // Find first non-framework class
            String simpleClassName = getSimpleClassName(className);
            return simpleClassName + ".method:123";
        }
        return "Not found";
    }
    
    /**
     * Analyze improved caller stack detection logic
     */
    private static void analyzeImprovedLogic() {
        System.out.println("Improved logic analysis:");

        // Mock typical call stack
        String[] mockStack = {
            "java.lang.Thread.getStackTrace",
            "com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger.getCallerInfo",
            "com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger.formatMessage",
            "com.p6spy.engine.spy.P6SpyPreparedStatement.execute",
            "com.zaxxer.hikari.pool.ProxyPreparedStatement.execute",
            "org.apache.ibatis.executor.statement.PreparedStatementHandler.query",
            "org.apache.ibatis.executor.statement.BaseStatementHandler.query",
            "org.apache.ibatis.executor.CachingExecutor.query",
            "org.apache.ibatis.session.defaults.DefaultSqlSession.selectList",
            "org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne",
            "org.apache.ibatis.binding.MapperMethod.execute",
            "org.apache.ibatis.binding.MapperProxy.invoke",
            "com.sun.proxy.$Proxy123.selectByUserId",
            "org.springframework.transaction.interceptor.TransactionInterceptor.invoke",
            "org.springframework.aop.framework.ReflectiveMethodInvocation.proceed",
            "org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke",
            "org.springframework.aop.framework.ReflectiveMethodInvocation.proceed",
            "org.springframework.aop.framework.JdkDynamicAopProxy.invoke",
            "com.sun.proxy.$Proxy124.selectByUserId",
            "com.tuowan.yeliao.user.manager.impl.UserMessageSettingManagerImpl.getByUserId",
            "com.tuowan.yeliao.user.service.impl.SignInServiceImpl.saveSignRemind",
            "com.tuowan.yeliao.user.controller.SignInController.remind"
        };

        System.out.println("Improved logic detection result:");
        String result = getImprovedLogicResult(mockStack);
        System.out.println("Detected caller: " + result);

        System.out.println("\nImprovement points:");
        System.out.println("1. Prioritize Service layer method calls");
        System.out.println("2. If no Service, look for Manager layer");
        System.out.println("3. If none, look for Controller layer");
        System.out.println("4. Skip all framework code and proxy classes");
    }
    
    /**
     * Improved logic detection result
     */
    private static String getImprovedLogicResult(String[] stack) {
        // First look for Service layer
        for (String className : stack) {
            if (isFrameworkClassImproved(className) || isProxyClassCurrent(className)) {
                continue;
            }

            if (className.contains(".service.") && className.contains("ServiceImpl")) {
                String simpleClassName = getSimpleClassName(className);
                return simpleClassName + ".saveSignRemind:158";  // Mock method name and line number
            }
        }

        // If no Service, look for Manager layer
        for (String className : stack) {
            if (isFrameworkClassImproved(className) || isProxyClassCurrent(className)) {
                continue;
            }

            if (className.contains(".manager.") && className.contains("ManagerImpl")) {
                String simpleClassName = getSimpleClassName(className);
                return simpleClassName + ".getByUserId:89";  // Mock method name and line number
            }
        }

        // If none, look for Controller layer
        for (String className : stack) {
            if (isFrameworkClassImproved(className) || isProxyClassCurrent(className)) {
                continue;
            }

            if (className.contains(".controller.") && className.contains("Controller")) {
                String simpleClassName = getSimpleClassName(className);
                return simpleClassName + ".remind:45";  // Mock method name and line number
            }
        }

        // Finally look for any business code
        for (String className : stack) {
            if (isFrameworkClassImproved(className) || isProxyClassCurrent(className)) {
                continue;
            }

            String simpleClassName = getSimpleClassName(className);
            return simpleClassName + ".method:123";
        }

        return "Not found";
    }
    
    /**
     * Current framework class detection logic
     */
    private static boolean isFrameworkClassCurrent(String className) {
        if (className == null) return true;
        
        String[] frameworkPackages = {
            "java.lang", "java.util", "java.sql", "javax.sql",
            "com.p6spy", "com.zaxxer.hikari",
            "org.apache.ibatis", "org.mybatis",
            "org.springframework.jdbc", "org.springframework.transaction", "org.springframework.aop",
            "org.springframework.cglib", "com.mysql.cj", "com.mysql.jdbc",
            "sun.reflect", "jdk.internal", "net.sf.cglib",
            "com.tuowan.yeliao.commons.config.p6spy",
            "com.github.pagehelper", "com.baomidou.mybatisplus", "com.easyooo.framework"
        };
        
        for (String pkg : frameworkPackages) {
            if (className.startsWith(pkg)) return true;
        }
        return false;
    }
    
    /**
     * Improved framework class detection logic
     */
    private static boolean isFrameworkClassImproved(String className) {
        if (className == null) return true;
        
        String[] frameworkPackages = {
            "java.lang", "java.util", "java.sql", "javax.sql",
            "com.p6spy", "com.zaxxer.hikari",
            "org.apache.ibatis", "org.mybatis",
            "org.springframework.jdbc", "org.springframework.transaction", 
            "org.springframework.aop", "org.springframework.dao.support",
            "org.springframework.cglib", "com.mysql.cj", "com.mysql.jdbc",
            "sun.reflect", "jdk.internal", "net.sf.cglib",
            "com.tuowan.yeliao.commons.config.p6spy",
            "com.github.pagehelper", "com.baomidou.mybatisplus", "com.easyooo.framework"
        };
        
        for (String pkg : frameworkPackages) {
            if (className.startsWith(pkg)) return true;
        }
        return false;
    }
    
    /**
     * Proxy class detection logic
     */
    private static boolean isProxyClassCurrent(String className) {
        if (className == null) return true;
        
        return className.contains("$Proxy") ||
               className.contains("$$") ||
               className.contains("$ByteBuddy$") ||
               className.contains("$MockitoMock$") ||
               className.contains("EnhancerBySpringCGLIB") ||
               className.contains("$FastClassBySpringCGLIB");
    }
    
    /**
     * Get simplified class name
     */
    private static String getSimpleClassName(String fullClassName) {
        if (fullClassName == null) return "Unknown";
        
        int lastDot = fullClassName.lastIndexOf('.');
        if (lastDot >= 0 && lastDot < fullClassName.length() - 1) {
            return fullClassName.substring(lastDot + 1);
        }
        return fullClassName;
    }
}
