import java.util.concurrent.ConcurrentHashMap;

public class TestUrlParsing {
    private static ConcurrentHashMap<String, String> urlToDatabaseNameCache = new ConcurrentHashMap<>();

    public static void main(String[] args) {
        String[] testUrls = {
            "jdbc:p6spy:mysql://**************:4417?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true",
            "jdbc:p6spy:mysql://**************:4417/yl_acct?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true",
            "jdbc:p6spy:mysql://**************:4417/yl_user?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true",
            "******************************************************************************",
            "*********************************************************************"
        };

        for (String url : testUrls) {
            String dbName = extractDatabaseName(url, 1);
            System.out.println("URL: " + url);
            System.out.println("Database: " + dbName);
            System.out.println("---");
        }
    }
    
    private static String extractDatabaseName(String url, int connectionId) {
        if (url == null || url.trim().isEmpty()) {
            return "UNKNOWN";
        }

        String cachedName = urlToDatabaseNameCache.get(url);
        if (cachedName != null) {
            return cachedName;
        }

        try {
            String actualUrl = url;
            if (url.startsWith("jdbc:p6spy:")) {
                actualUrl = url.substring("jdbc:p6spy:".length());
                if (!actualUrl.startsWith("jdbc:")) {
                    actualUrl = "jdbc:" + actualUrl;
                }
            }

            if (actualUrl.contains("://")) {
                String[] parts = actualUrl.split("://");
                if (parts.length > 1) {
                    String hostAndPath = parts[1];

                    int queryStart = hostAndPath.indexOf('?');
                    int pathStart = hostAndPath.indexOf('/');

                    if (pathStart == -1) {
                        urlToDatabaseNameCache.put(url, "NO_DB");
                        return "NO_DB";
                    }

                    if (pathStart >= 0 && pathStart < hostAndPath.length() - 1) {
                        String dbPart = hostAndPath.substring(pathStart + 1);

                        if (queryStart > pathStart) {
                            dbPart = dbPart.substring(0, queryStart - pathStart - 1);
                        }

                        String actualDbName = dbPart.trim();

                        if (actualDbName.isEmpty()) {
                            urlToDatabaseNameCache.put(url, "NO_DB");
                            return "NO_DB";
                        }

                        urlToDatabaseNameCache.put(url, actualDbName);
                        return actualDbName;
                    }
                }
            }
        } catch (Exception e) {
            String fallbackName = "CONN-" + connectionId;
            urlToDatabaseNameCache.put(url, fallbackName);
            return fallbackName;
        }

        String fallbackName = "CONN-" + connectionId;
        urlToDatabaseNameCache.put(url, fallbackName);
        return fallbackName;
    }
}
