import java.lang.reflect.Method;

/**
 * 测试P6SpyLogger修复效果
 */
public class TestP6SpyFix {
    
    public static void main(String[] args) {
        try {
            // 测试数据库名映射
            testDatabaseMapping();
            
            // 测试调用栈过滤
            testCallerInfoFiltering();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static void testDatabaseMapping() throws Exception {
        System.out.println("=== 测试数据库名映射 ===");
        
        // 模拟不同的数据库URL
        String[] testUrls = {
            "*******************************************************",
            "******************************************************",
            "******************************************************",
            "********************************************************",
            "*********************************************************"
        };
        
        // 使用反射调用私有方法进行测试
        Class<?> loggerClass = Class.forName("com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger");
        Object logger = loggerClass.newInstance();
        Method extractMethod = loggerClass.getDeclaredMethod("extractDatabaseName", String.class, int.class);
        extractMethod.setAccessible(true);
        
        for (String url : testUrls) {
            String result = (String) extractMethod.invoke(logger, url, 1);
            System.out.println(url + " -> " + result);
        }
    }
    
    private static void testCallerInfoFiltering() throws Exception {
        System.out.println("\n=== 测试调用栈过滤 ===");
        
        // 模拟调用栈
        StackTraceElement[] mockStack = {
            new StackTraceElement("java.lang.Thread", "getStackTrace", "Thread.java", 1559),
            new StackTraceElement("com.p6spy.engine.spy.P6SpyDriver", "connect", "P6SpyDriver.java", 123),
            new StackTraceElement("com.github.pagehelper.PaginationPlugin", "intercept", "PaginationPlugin.java", 66),
            new StackTraceElement("org.apache.ibatis.executor.SimpleExecutor", "query", "SimpleExecutor.java", 89),
            new StackTraceElement("com.tuowan.yeliao.user.service.acct.AcctService", "findUserById", "AcctService.java", 45),
            new StackTraceElement("com.tuowan.yeliao.user.controller.UserController", "getUser", "UserController.java", 23)
        };
        
        // 使用反射调用私有方法进行测试
        Class<?> loggerClass = Class.forName("com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger");
        Object logger = loggerClass.newInstance();
        Method isFrameworkMethod = loggerClass.getDeclaredMethod("isFrameworkClass", String.class);
        isFrameworkMethod.setAccessible(true);
        
        for (StackTraceElement element : mockStack) {
            boolean isFramework = (Boolean) isFrameworkMethod.invoke(logger, element.getClassName());
            System.out.println(element.getClassName() + " -> " + (isFramework ? "框架类(跳过)" : "业务类(保留)"));
        }
    }
}
