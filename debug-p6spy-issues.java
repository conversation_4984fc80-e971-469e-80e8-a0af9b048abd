import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.P6spyConfig;
import com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger;
import com.easyooo.framework.common.util.PropUtils;

import java.util.Properties;

/**
 * P6Spy问题调试工具
 * 用于诊断当前遇到的三个问题：
 * 1. [SQL-UNKNOWN] 环境未正确读取
 * 2. 数据库: SHANGHAI 显示错误的数据库名
 * 3. 调用: $Proxy320.query:-1 显示代理类信息
 */
public class DebugP6spyIssues {
    
    public static void main(String[] args) {
        System.out.println("=== P6Spy 问题诊断工具 ===\n");
        
        // 问题1: 环境检测
        debugEnvironmentDetection();
        
        // 问题2: 数据库名称提取
        debugDatabaseNameExtraction();
        
        // 问题3: 调用栈信息
        debugCallerInfo();
        
        // 解决方案建议
        provideSolutions();
    }
    
    /**
     * 调试环境检测问题
     */
    private static void debugEnvironmentDetection() {
        System.out.println("=== 问题1: 环境检测 ===");
        
        // 检查系统属性
        String springProfiles = System.getProperty("spring.profiles.active");
        System.out.println("System.getProperty(\"spring.profiles.active\"): " + springProfiles);
        
        // 检查环境变量
        String envProfiles = System.getenv("SPRING_PROFILES_ACTIVE");
        System.out.println("System.getenv(\"SPRING_PROFILES_ACTIVE\"): " + envProfiles);
        
        // 检查AppConfig.ENV
        try {
            System.out.println("AppConfig.ENV: " + AppConfig.ENV);
            System.out.println("UnifiedConfig.isDevEnv(): " + UnifiedConfig.isDevEnv());
            System.out.println("UnifiedConfig.isTestEnv(): " + UnifiedConfig.isTestEnv());
            System.out.println("UnifiedConfig.isProdEnv(): " + UnifiedConfig.isProdEnv());
        } catch (Exception e) {
            System.out.println("AppConfig检查异常: " + e.getMessage());
        }
        
        // 检查P6SpyLogger的环境获取逻辑
        System.out.println("\n--- P6SpyLogger环境检测逻辑 ---");
        String profiles = System.getProperty("spring.profiles.active");
        if (profiles == null || profiles.trim().isEmpty()) {
            profiles = System.getenv("SPRING_PROFILES_ACTIVE");
        }
        
        if (profiles != null && !profiles.trim().isEmpty()) {
            String env = profiles.split(",")[0].trim().toUpperCase();
            System.out.println("P6SpyLogger检测到的环境: " + env);
        } else {
            System.out.println("P6SpyLogger检测到的环境: UNKNOWN (未找到spring.profiles.active)");
        }
        
        System.out.println();
    }
    
    /**
     * 调试数据库名称提取问题
     */
    private static void debugDatabaseNameExtraction() {
        System.out.println("=== 问题2: 数据库名称提取 ===");
        
        // 模拟常见的JDBC URL
        String[] testUrls = {
            "************************************************************************************",
            "********************************************************",
            "******************************************************",
            "************************************************"
        };
        
        for (String url : testUrls) {
            String extractedName = extractDatabaseNameDebug(url);
            System.out.println("URL: " + url);
            System.out.println("提取的数据库名: " + extractedName);
            System.out.println();
        }
    }
    
    /**
     * 调试版本的数据库名称提取方法
     */
    private static String extractDatabaseNameDebug(String url) {
        if (url == null || url.trim().isEmpty()) {
            return "UNKNOWN";
        }
        
        try {
            // 处理p6spy包装的URL
            String actualUrl = url;
            if (url.startsWith("jdbc:p6spy:")) {
                actualUrl = url.substring("jdbc:p6spy:".length());
                if (!actualUrl.startsWith("jdbc:")) {
                    actualUrl = "jdbc:" + actualUrl;
                }
            }
            
            System.out.println("  处理后的URL: " + actualUrl);
            
            // 从URL中提取数据库名称
            if (actualUrl.contains("/") && actualUrl.contains("://")) {
                String[] parts = actualUrl.split("://");
                if (parts.length > 1) {
                    String hostAndDb = parts[1];
                    System.out.println("  hostAndDb: " + hostAndDb);
                    
                    // 找到最后一个/后面的数据库名
                    int lastSlash = hostAndDb.lastIndexOf('/');
                    if (lastSlash >= 0 && lastSlash < hostAndDb.length() - 1) {
                        String dbPart = hostAndDb.substring(lastSlash + 1);
                        System.out.println("  dbPart: " + dbPart);
                        
                        // 去掉URL参数
                        int questionMark = dbPart.indexOf('?');
                        if (questionMark >= 0) {
                            dbPart = dbPart.substring(0, questionMark);
                        }
                        
                        System.out.println("  最终数据库名: " + dbPart);
                        return dbPart.trim().toUpperCase();
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("  解析异常: " + e.getMessage());
        }
        
        return "UNKNOWN";
    }
    
    /**
     * 调试调用栈信息问题
     */
    private static void debugCallerInfo() {
        System.out.println("=== 问题3: 调用栈信息 ===");
        
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        
        System.out.println("当前调用栈:");
        for (int i = 0; i < Math.min(stackTrace.length, 10); i++) {
            StackTraceElement element = stackTrace[i];
            String className = element.getClassName();
            String methodName = element.getMethodName();
            int lineNumber = element.getLineNumber();
            
            boolean isFramework = isFrameworkClassDebug(className);
            System.out.printf("  [%d] %s.%s:%d (框架类: %s)\n", 
                i, className, methodName, lineNumber, isFramework);
        }
        
        System.out.println();
    }
    
    /**
     * 调试版本的框架类判断方法
     */
    private static boolean isFrameworkClassDebug(String className) {
        if (className == null) {
            return true;
        }
        
        String[] frameworkPackages = {
            "java.lang",
            "java.util", 
            "java.sql",
            "javax.sql",
            "com.p6spy",
            "com.zaxxer.hikari",
            "org.apache.ibatis",
            "org.mybatis",
            "org.springframework.jdbc",
            "org.springframework.transaction",
            "org.springframework.aop",
            "org.springframework.cglib",
            "com.mysql.cj",
            "com.mysql.jdbc",
            "sun.reflect",
            "jdk.internal",
            "net.sf.cglib",
            "com.tuowan.yeliao.commons.config.p6spy",
            "com.github.pagehelper",
            "com.baomidou.mybatisplus",
            "com.easyooo.framework"
        };
        
        for (String pkg : frameworkPackages) {
            if (className.startsWith(pkg)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 提供解决方案建议
     */
    private static void provideSolutions() {
        System.out.println("=== 解决方案建议 ===");
        
        System.out.println("问题1 - [SQL-UNKNOWN] 环境未读取:");
        System.out.println("  原因: P6SpyLogger在静态初始化时spring.profiles.active可能还未设置");
        System.out.println("  解决: 改为动态获取环境信息，而不是静态缓存");
        System.out.println();
        
        System.out.println("问题2 - 数据库名显示SHANGHAI:");
        System.out.println("  原因: URL中的数据库名就是'shanghai'，被转换为大写显示");
        System.out.println("  解决: 需要建立物理数据库名到逻辑名称的映射");
        System.out.println();
        
        System.out.println("问题3 - 调用信息显示$Proxy320.query:-1:");
        System.out.println("  原因: 显示的是动态代理类信息，不是实际业务代码");
        System.out.println("  解决: 需要过滤掉代理类，找到真正的业务调用点");
        System.out.println();
    }
}
