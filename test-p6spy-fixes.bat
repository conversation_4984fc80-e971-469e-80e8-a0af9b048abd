@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo === 测试P6SpyLogger修复效果 ===
echo.

REM 设置环境变量
set "SPRING_PROFILES_ACTIVE=dev"

REM 编译测试文件
echo 🔧 编译测试文件...
javac -cp "yl-commons\yl-commons-config\target\classes;yl-commons\yl-commons-context\target\classes;yl-commons\yl-commons-entity\target\classes;%USERPROFILE%\.m2\repository\com\easyooo\easyooo-framework\1.0.0\easyooo-framework-1.0.0.jar;%USERPROFILE%\.m2\repository\p6spy\p6spy\3.9.1\p6spy-3.9.1.jar" test-p6spy-fixes.java

if !errorlevel! neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo ✅ 编译成功
echo.

REM 运行测试
echo 🚀 运行测试...
java -cp ".;yl-commons\yl-commons-config\target\classes;yl-commons\yl-commons-context\target\classes;yl-commons\yl-commons-entity\target\classes;%USERPROFILE%\.m2\repository\com\easyooo\easyooo-framework\1.0.0\easyooo-framework-1.0.0.jar;%USERPROFILE%\.m2\repository\p6spy\p6spy\3.9.1\p6spy-3.9.1.jar" TestP6SpyFixes

echo.
echo === 测试完成 ===

REM 清理编译文件
if exist "TestP6SpyFixes.class" del "TestP6SpyFixes.class"

pause
