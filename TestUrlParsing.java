import java.util.concurrent.ConcurrentHashMap;

public class TestUrlParsing {
    private static ConcurrentHashMap<String, String> urlToDatabaseNameCache = new ConcurrentHashMap<>();
    
    public static void main(String[] args) {
        String[] testUrls = {
            "*************************************************************************************************************",
            "*********************************************************************************************************************",
            "*********************************************************************************************************************",
            "******************************************************************************",
            "*********************************************************************"
        };
        
        for (String url : testUrls) {
            String dbName = extractDatabaseName(url, 1);
            System.out.println("URL: " + url);
            System.out.println("Database: " + dbName);
            System.out.println("---");
        }
    }
    
    private static String extractDatabaseName(String url, int connectionId) {
        if (url == null || url.trim().isEmpty()) {
            return "UNKNOWN";
        }

        String cachedName = urlToDatabaseNameCache.get(url);
        if (cachedName != null) {
            return cachedName;
        }

        try {
            String actualUrl = url;
            if (url.startsWith("jdbc:p6spy:")) {
                actualUrl = url.substring("jdbc:p6spy:".length());
                if (!actualUrl.startsWith("jdbc:")) {
                    actualUrl = "jdbc:" + actualUrl;
                }
            }

            if (actualUrl.contains("://")) {
                String[] parts = actualUrl.split("://");
                if (parts.length > 1) {
                    String hostAndPath = parts[1];
                    System.out.println("  hostAndPath: " + hostAndPath);

                    // First find where parameters start (?)
                    int queryStart = hostAndPath.indexOf('?');
                    String hostPortPath;
                    if (queryStart >= 0) {
                        hostPortPath = hostAndPath.substring(0, queryStart);
                    } else {
                        hostPortPath = hostAndPath;
                    }
                    System.out.println("  hostPortPath: " + hostPortPath);

                    // Now find the path separator (/) in the host:port/database part
                    int pathStart = hostPortPath.indexOf('/');
                    System.out.println("  pathStart: " + pathStart);

                    // If no path part (no /), means no database name specified
                    if (pathStart == -1) {
                        urlToDatabaseNameCache.put(url, "NO_DB");
                        return "NO_DB";
                    }

                    // Extract database part after /
                    if (pathStart >= 0 && pathStart < hostPortPath.length() - 1) {
                        String actualDbName = hostPortPath.substring(pathStart + 1).trim();

                        // If database name is empty, means URL format is /?
                        if (actualDbName.isEmpty()) {
                            urlToDatabaseNameCache.put(url, "NO_DB");
                            return "NO_DB";
                        }

                        urlToDatabaseNameCache.put(url, actualDbName);
                        return actualDbName;
                    }
                }
            }
        } catch (Exception e) {
            String fallbackName = "CONN-" + connectionId;
            urlToDatabaseNameCache.put(url, fallbackName);
            return fallbackName;
        }

        String fallbackName = "CONN-" + connectionId;
        urlToDatabaseNameCache.put(url, fallbackName);
        return fallbackName;
    }
}
