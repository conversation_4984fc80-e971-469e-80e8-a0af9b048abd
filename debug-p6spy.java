import com.tuowan.yeliao.commons.config.configuration.impl.P6spyConfig;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.easyooo.framework.common.util.PropUtils;
import java.util.Properties;

/**
 * P6Spy调试工具
 */
public class DebugP6Spy {
    
    public static void main(String[] args) {
        System.out.println("=== P6Spy 调试信息 ===");
        
        // 1. 检查环境
        System.out.println("1. 环境检查:");
        System.out.println("   - isProdEnv: " + UnifiedConfig.isProdEnv());
        System.out.println("   - isDevEnv: " + UnifiedConfig.isDevEnv());
        System.out.println("   - isTestEnv: " + UnifiedConfig.isTestEnv());
        
        // 2. 检查系统属性
        System.out.println("\n2. 系统属性:");
        String springProfiles = System.getProperty("spring.profiles.active");
        if (springProfiles == null) {
            springProfiles = System.getenv("SPRING_PROFILES_ACTIVE");
        }
        System.out.println("   - spring.profiles.active: " + springProfiles);
        
        // 3. 检查配置
        System.out.println("\n3. 配置检查:");
        try {
            Properties props = UnifiedConfig.getProperties();
            System.out.println("   - 配置属性数量: " + props.size());
            
            // 检查p6spy.enabled配置
            boolean p6spyEnabled = PropUtils.getBoolean(props, "p6spy", "enabled");
            System.out.println("   - p6spy.enabled: " + p6spyEnabled);
            
            // 列出所有p6spy相关配置
            System.out.println("   - p6spy相关配置:");
            for (String key : props.stringPropertyNames()) {
                if (key.toLowerCase().contains("p6spy")) {
                    System.out.println("     " + key + " = " + props.getProperty(key));
                }
            }
            
        } catch (Exception e) {
            System.out.println("   - 配置读取异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 4. 检查P6SpyConfig状态
        System.out.println("\n4. P6SpyConfig状态:");
        try {
            boolean enabled = P6spyConfig.isEnabled();
            System.out.println("   - P6spyConfig.isEnabled(): " + enabled);
        } catch (Exception e) {
            System.out.println("   - P6spyConfig检查异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 5. 检查p6spy类是否存在
        System.out.println("\n5. P6Spy类检查:");
        try {
            Class.forName("com.p6spy.engine.spy.P6SpyDriver");
            System.out.println("   - P6SpyDriver类: 存在");
        } catch (ClassNotFoundException e) {
            System.out.println("   - P6SpyDriver类: 不存在");
        }
        
        try {
            Class.forName("com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger");
            System.out.println("   - P6SpyLogger类: 存在");
        } catch (ClassNotFoundException e) {
            System.out.println("   - P6SpyLogger类: 不存在");
        }
        
        // 6. 检查spy.properties文件
        System.out.println("\n6. spy.properties文件检查:");
        try {
            java.io.InputStream is = DebugP6Spy.class.getClassLoader().getResourceAsStream("spy.properties");
            if (is != null) {
                System.out.println("   - spy.properties: 存在");
                is.close();
            } else {
                System.out.println("   - spy.properties: 不存在");
            }
        } catch (Exception e) {
            System.out.println("   - spy.properties检查异常: " + e.getMessage());
        }
        
        System.out.println("\n=== 调试完成 ===");
    }
}
