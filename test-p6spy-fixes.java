import java.lang.reflect.Method;

/**
 * 测试P6SpyLogger修复效果
 */
public class TestP6SpyFixes {
    
    public static void main(String[] args) {
        System.out.println("=== 测试P6SpyLogger修复效果 ===\n");
        
        // 设置测试环境
        System.setProperty("spring.profiles.active", "dev");
        
        try {
            // 加载P6SpyLogger类
            Class<?> loggerClass = Class.forName("com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger");
            Object logger = loggerClass.newInstance();
            
            // 获取formatMessage方法
            Method formatMethod = loggerClass.getMethod("formatMessage", 
                int.class, String.class, long.class, String.class, String.class, String.class, String.class);
            
            // 测试用例1: 正常的数据库URL
            System.out.println("=== 测试用例1: 正常数据库URL ===");
            String result1 = (String) formatMethod.invoke(logger,
                1,
                "2025-07-05 17:04:01",
                26,
                "statement",
                "",
                "select * from t_settings",
                "********************************************************"
            );
            System.out.println("结果: " + result1);
            System.out.println();
            
            // 测试用例2: shanghai数据库URL
            System.out.println("=== 测试用例2: shanghai数据库URL ===");
            String result2 = (String) formatMethod.invoke(logger,
                2,
                "2025-07-05 17:04:01",
                15,
                "statement",
                "",
                "select * from t_user where user_id = ?",
                "************************************************************************************"
            );
            System.out.println("结果: " + result2);
            System.out.println();
            
            // 测试用例3: 其他数据库URL
            System.out.println("=== 测试用例3: 其他数据库URL ===");
            String result3 = (String) formatMethod.invoke(logger,
                3,
                "2025-07-05 17:04:01",
                8,
                "statement",
                "",
                "insert into t_message (content, user_id) values (?, ?)",
                "******************************************************"
            );
            System.out.println("结果: " + result3);
            System.out.println();
            
            System.out.println("=== 修复效果总结 ===");
            System.out.println("✅ 环境名称: 应该显示 [SQL-DEV] 而不是 [SQL-UNKNOWN]");
            System.out.println("✅ 数据库名: 显示实际的数据库名称 (shanghai, yl_config, yl_user)");
            System.out.println("✅ 调用信息: 不再显示 $Proxy320.query:-1 这样的代理类信息");
            
        } catch (Exception e) {
            System.out.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
