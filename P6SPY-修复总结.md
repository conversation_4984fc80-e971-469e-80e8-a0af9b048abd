# P6Spy问题修复总结

## 🎯 修复的问题

您遇到的三个P6Spy问题已全部修复：

### 1. ❌ [SQL-UNKNOWN] 环境未正确读取
**问题**: 显示 `[SQL-UNKNOWN]` 而不是 `[SQL-DEV]`
**原因**: P6SpyLogger在静态初始化时，spring.profiles.active可能还未设置
**修复**: 改为动态获取环境信息

```java
// 修复前：静态获取
private static String environmentName;
static {
    environmentName = getEnvironmentName();
}

// 修复后：动态获取
private String getEnvironmentName() {
    try {
        // 优先从AppConfig获取环境信息
        Class<?> appConfigClass = Class.forName("com.tuowan.yeliao.commons.config.configuration.impl.AppConfig");
        Object env = appConfigClass.getField("ENV").get(null);
        if (env != null) {
            return env.toString().toUpperCase();
        }
    } catch (Exception e) {
        // 回退到系统属性
    }
    
    String profiles = System.getProperty("spring.profiles.active");
    if (profiles == null || profiles.trim().isEmpty()) {
        profiles = System.getenv("SPRING_PROFILES_ACTIVE");
    }
    
    if (profiles != null && !profiles.trim().isEmpty()) {
        return profiles.split(",")[0].trim().toUpperCase();
    }
    
    return "DEV"; // 默认为DEV环境
}
```

### 2. ❌ 数据库: SHANGHAI 显示错误的数据库名
**问题**: 显示 `数据库: SHANGHAI` 而不是实际的数据库名
**原因**: URL解析算法错误，从 `serverTimezone=Asia/Shanghai` 参数中错误提取了"Shanghai"
**修复**: 修正URL解析逻辑，先分离参数部分，再从主机:端口/数据库部分提取数据库名

```java
// 修复前：错误的解析逻辑
int pathStart = hostAndPath.indexOf('/'); // 会找到参数中的/

// 修复后：正确的解析逻辑
// 先找到参数开始位置（?），避免从参数中错误提取数据库名
int queryStart = hostAndPath.indexOf('?');
String hostPortPath;
if (queryStart >= 0) {
    hostPortPath = hostAndPath.substring(0, queryStart);
} else {
    hostPortPath = hostAndPath;
}

// 在主机:端口/数据库部分查找路径分隔符（/）
int pathStart = hostPortPath.indexOf('/');

// 如果没有数据库名，返回"NO_DB"
if (pathStart == -1) {
    return "NO_DB";
}
```

### 3. ❌ 调用: $Proxy320.query:-1 显示代理类信息
**问题**: 显示 `调用: $Proxy320.query:-1` 这样的代理类信息
**原因**: 显示的是动态代理类信息，不是实际业务代码
**修复**: 过滤掉代理类，找到真正的业务调用点

```java
// 新增代理类检测方法
private boolean isProxyClass(String className) {
    if (className == null) {
        return true;
    }
    
    return className.contains("$Proxy") || 
           className.contains("$$") ||  // CGLIB代理
           className.contains("$ByteBuddy$") ||  // ByteBuddy代理
           className.contains("$MockitoMock$") ||  // Mockito代理
           className.contains("EnhancerBySpringCGLIB") ||  // Spring CGLIB代理
           className.contains("$FastClassBySpringCGLIB");  // Spring FastClass
}

// 修改调用栈检测逻辑
for (StackTraceElement element : stackTrace) {
    String className = element.getClassName();
    String methodName = element.getMethodName();
    int lineNumber = element.getLineNumber();

    // 跳过框架相关的类和代理类
    if (isFrameworkClass(className) || isProxyClass(className)) {
        continue;
    }

    // 找到业务代码，返回简化的类名.方法名:行号
    String simpleClassName = getSimpleClassName(className);
    return String.format("%s.%s:%d", simpleClassName, methodName, lineNumber);
}
```

### 4. 🎨 优化日志格式
**改进**: 当没有调用信息时，不显示调用部分，使日志更简洁

```java
// 格式化日志，如果没有调用信息就不显示调用部分
if (StringUtils.isBlank(callerInfo)) {
    return String.format("[SQL-%s] %s | 执行时间: %dms | 数据库: %s | SQL: %s",
            env, now, elapsed, databaseName, cleanSql);
} else {
    return String.format("[SQL-%s] %s | 执行时间: %dms | 数据库: %s | 调用: %s | SQL: %s",
            env, now, elapsed, databaseName, callerInfo, cleanSql);
}
```

## ✅ 修复效果

修复后的P6Spy日志格式将显示为：

```
[SQL-DEV] 2025-07-05 17:04:01 | 执行时间: 26ms | 数据库: NO_DB | SQL: select * from t_settings
[SQL-DEV] 2025-07-05 17:04:01 | 执行时间: 15ms | 数据库: yl_config | SQL: select * from t_user where user_id = ?
[SQL-DEV] 2025-07-05 17:04:01 | 执行时间: 8ms | 数据库: yl_user | 调用: UserService.getUserById:45 | SQL: insert into t_message (content, user_id) values (?, ?)
```

**说明**:
- 对于没有指定数据库名的URL（如 `*****************************`），显示 `NO_DB`
- 对于有数据库名的URL（如 `**************************************`），显示实际的数据库名

## 🔧 修改的文件

- `yl-commons/yl-commons-config/src/main/java/com/tuowan/yeliao/commons/config/p6spy/P6SpyLogger.java`

## 🚀 使用方法

1. 重新编译项目：`mvn clean compile -Pdev`
2. 启动应用，P6Spy将自动使用修复后的日志格式
3. 执行SQL操作，查看控制台输出的SQL日志

## 📝 注意事项

- 修复保持了代码的简洁性和扩展性
- 没有增加额外的配置文件或复杂的映射逻辑
- 环境检测更加可靠，支持多种获取方式
- 代理类过滤更加全面，支持各种代理框架

所有问题已修复完成，您可以重新启动应用测试效果！
